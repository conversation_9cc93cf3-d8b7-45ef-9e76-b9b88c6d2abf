namespace GMCadiomJsonProvider.Tests.Extensions;

/// <summary>
/// Tests for ASP.NET Core integration extensions.
/// </summary>
public class AspNetCoreIntegrationTests
{
    [Fact]
    public void AddGMCadiomJson_ToMvcBuilder_ShouldRegisterJsonProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var mvcBuilder = services.AddControllers();

        // Act
        mvcBuilder.AddGMCadiomJson(config =>
        {
            config.WriteIndented = true;
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddGMCadiomMicrosoftJson_ToMvcBuilder_ShouldUseMicrosoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var mvcBuilder = services.AddControllers();

        // Act
        mvcBuilder.AddGMCadiomMicrosoftJson(config =>
        {
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
        jsonProvider.ProviderName.Should().Be("Microsoft");
    }

    [Fact]
    public void AddGMCadiomNewtonsoftJson_ToMvcBuilder_ShouldUseNewtonsoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var mvcBuilder = services.AddControllers();

        // Act
        mvcBuilder.AddGMCadiomNewtonsoftJson(config =>
        {
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        jsonProvider.ProviderName.Should().Be("Newtonsoft");
    }

    [Fact]
    public void AddGMCadiomJson_ToSignalRBuilder_ShouldRegisterJsonProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var signalRBuilder = services.AddSignalR();

        // Act
        signalRBuilder.AddGMCadiomJsonProtocol(config =>
        {
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            config.WriteIndented = false;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddGMCadiomMicrosoftJsonProtocol_ToSignalRBuilder_ShouldUseMicrosoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var signalRBuilder = services.AddSignalR();

        // Act
        signalRBuilder.AddGMCadiomMicrosoftJsonProtocol(config =>
        {
            config.PropertyNameCaseInsensitive = true;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddGMCadiomNewtonsoftJsonProtocol_ToSignalRBuilder_ShouldUseNewtonsoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var signalRBuilder = services.AddSignalR();

        // Act
        signalRBuilder.AddGMCadiomNewtonsoftJsonProtocol(config =>
        {
            config.DateFormatHandling = JsonDateFormatHandling.IsoDateFormat;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void AddGMCadiomJson_ToWebApplicationBuilder_ShouldRegisterJsonProvider()
    {
        // Arrange
        var builder = WebApplication.CreateBuilder();

        // Act
        builder.AddGMCadiomJson(config =>
        {
            config.WriteIndented = true;
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        });

        // Assert
        var jsonProvider = builder.Services.BuildServiceProvider().GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddGMCadiomMicrosoftJson_ToWebApplicationBuilder_ShouldUseMicrosoftProvider()
    {
        // Arrange
        var builder = WebApplication.CreateBuilder();

        // Act
        builder.AddGMCadiomMicrosoftJson(config =>
        {
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        });

        // Assert
        var jsonProvider = builder.Services.BuildServiceProvider().GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddGMCadiomNewtonsoftJson_ToWebApplicationBuilder_ShouldUseNewtonsoftProvider()
    {
        // Arrange
        var builder = WebApplication.CreateBuilder();

        // Act
        builder.AddGMCadiomNewtonsoftJson(config =>
        {
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        });

        // Assert
        var jsonProvider = builder.Services.BuildServiceProvider().GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void AddGMCadiomJson_ToHttpClientBuilder_ShouldRegisterJsonProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var httpClientBuilder = services.AddHttpClient("TestClient");

        // Act
        httpClientBuilder.AddGMCadiomJson(config =>
        {
            config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddGMCadiomMicrosoftJson_ToHttpClientBuilder_ShouldUseMicrosoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var httpClientBuilder = services.AddHttpClient("TestClient");

        // Act
        httpClientBuilder.AddGMCadiomMicrosoftJson(config =>
        {
            config.WriteIndented = false;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }

    [Fact]
    public void AddGMCadiomNewtonsoftJson_ToHttpClientBuilder_ShouldUseNewtonsoftProvider()
    {
        // Arrange
        var services = new ServiceCollection();
        var httpClientBuilder = services.AddHttpClient("TestClient");

        // Act
        httpClientBuilder.AddGMCadiomNewtonsoftJson(config =>
        {
            config.PropertyNameCaseInsensitive = true;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void AddGMCadiomJsonFromConfig_ToSignalRBuilder_ShouldUseConfigurationFromJson()
    {
        // Arrange
        var services = new ServiceCollection();
        var signalRBuilder = services.AddSignalR();
        var jsonConfig = """
        {
            "providerType": "Newtonsoft",
            "writeIndented": false,
            "propertyNamingPolicy": "CamelCase",
            "nullValueHandling": "Ignore"
        }
        """;

        // Act
        signalRBuilder.AddGMCadiomJsonProtocolFromConfig(jsonConfig);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
    }

    [Fact]
    public void AddGMCadiomJsonFromConfig_ToHttpClientBuilder_ShouldUseConfigurationFromJson()
    {
        // Arrange
        var services = new ServiceCollection();
        var httpClientBuilder = services.AddHttpClient("TestClient");
        var jsonConfig = """
        {
            "providerType": "Microsoft",
            "writeIndented": true,
            "propertyNamingPolicy": "CamelCase"
        }
        """;

        // Act
        httpClientBuilder.AddGMCadiomJsonFromConfig(jsonConfig);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jsonProvider = serviceProvider.GetService<IJsonProvider>();

        jsonProvider.Should().NotBeNull();
        jsonProvider!.ProviderType.Should().Be(JsonProviderType.Microsoft);
    }
}
