namespace GMCadiomJsonProvider.Extensions;

/// <summary>
/// Extension methods for IMvcBuilder to integrate GMCadiomJsonProvider with ASP.NET Core MVC.
/// </summary>
public static class MvcBuilderExtensions
{
    /// <summary>
    /// Configures MVC to use GMCadiomJsonProvider with the default Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="builder">The IMvcBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The IMvcBuilder for method chaining.</returns>
    public static IMvcBuilder AddGMCadiomJson(this IMvcBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Configures MVC to use GMCadiomJsonProvider with the specified provider type.
    /// </summary>
    /// <param name="builder">The IMvcBuilder instance.</param>
    /// <param name="providerType">The JSON provider type to use.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The IMvcBuilder for method chaining.</returns>
    public static IMvcBuilder AddGMCadiomJson(this IMvcBuilder builder, JsonProviderType providerType, Action<JsonConfiguration>? configure = null)
    {
        var config = new JsonConfiguration { ProviderType = providerType };
        configure?.Invoke(config);

        // Register the JSON provider
        builder.Services.AddJsonProvider(config);

        // Configure MVC JSON options based on provider type
        if (providerType == JsonProviderType.Microsoft)
        {
            builder.AddJsonOptions(options =>
            {
                var systemTextJsonOptions = config.ToSystemTextJsonOptions();
                options.JsonSerializerOptions.WriteIndented = systemTextJsonOptions.WriteIndented;
                options.JsonSerializerOptions.PropertyNameCaseInsensitive = systemTextJsonOptions.PropertyNameCaseInsensitive;
                options.JsonSerializerOptions.AllowTrailingCommas = systemTextJsonOptions.AllowTrailingCommas;
                options.JsonSerializerOptions.DefaultIgnoreCondition = systemTextJsonOptions.DefaultIgnoreCondition;
                options.JsonSerializerOptions.PropertyNamingPolicy = systemTextJsonOptions.PropertyNamingPolicy;
                options.JsonSerializerOptions.DictionaryKeyPolicy = systemTextJsonOptions.DictionaryKeyPolicy;
                options.JsonSerializerOptions.IncludeFields = systemTextJsonOptions.IncludeFields;
                options.JsonSerializerOptions.MaxDepth = systemTextJsonOptions.MaxDepth;
                options.JsonSerializerOptions.ReadCommentHandling = systemTextJsonOptions.ReadCommentHandling;
                options.JsonSerializerOptions.NumberHandling = systemTextJsonOptions.NumberHandling;
                options.JsonSerializerOptions.Encoder = systemTextJsonOptions.Encoder;
            });
        }
        else if (providerType == JsonProviderType.Newtonsoft)
        {
            builder.AddNewtonsoftJson(options =>
            {
                var newtonsoftSettings = config.ToNewtonsoftJsonSettings();
                options.SerializerSettings.Formatting = newtonsoftSettings.Formatting;
                options.SerializerSettings.NullValueHandling = newtonsoftSettings.NullValueHandling;
                options.SerializerSettings.DateFormatHandling = newtonsoftSettings.DateFormatHandling;
                options.SerializerSettings.MissingMemberHandling = newtonsoftSettings.MissingMemberHandling;
                options.SerializerSettings.ReferenceLoopHandling = newtonsoftSettings.ReferenceLoopHandling;
                options.SerializerSettings.ContractResolver = newtonsoftSettings.ContractResolver;
                options.SerializerSettings.Culture = newtonsoftSettings.Culture;
                options.SerializerSettings.MaxDepth = newtonsoftSettings.MaxDepth;
            });
        }

        return builder;
    }

    /// <summary>
    /// Configures MVC to use GMCadiomJsonProvider with Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="builder">The IMvcBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The IMvcBuilder for method chaining.</returns>
    public static IMvcBuilder AddGMCadiomMicrosoftJson(this IMvcBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Configures MVC to use GMCadiomJsonProvider with Newtonsoft.Json provider.
    /// </summary>
    /// <param name="builder">The IMvcBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The IMvcBuilder for method chaining.</returns>
    public static IMvcBuilder AddGMCadiomNewtonsoftJson(this IMvcBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJson(JsonProviderType.Newtonsoft, configure);
    }

    /// <summary>
    /// Configures MVC to use GMCadiomJsonProvider using a fluent builder pattern.
    /// </summary>
    /// <param name="builder">The IMvcBuilder instance.</param>
    /// <param name="configure">Action to configure the JSON provider using the builder.</param>
    /// <returns>The IMvcBuilder for method chaining.</returns>
    public static IMvcBuilder AddGMCadiomJson(this IMvcBuilder builder, Action<IJsonProviderBuilder> configure)
    {
        var jsonBuilder = new JsonProviderBuilder();
        configure(jsonBuilder);
        var provider = jsonBuilder.Build();

        // Register the built provider
        builder.Services.AddSingleton<IJsonProvider>(provider);

        // Get the configuration from the provider to configure MVC
        var config = GetConfigurationFromProvider(provider);

        return builder.AddGMCadiomJson(config.ProviderType, _ => { });
    }

    /// <summary>
    /// Configures MVC to use GMCadiomJsonProvider from a JSON configuration string.
    /// </summary>
    /// <param name="builder">The IMvcBuilder instance.</param>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>The IMvcBuilder for method chaining.</returns>
    public static IMvcBuilder AddGMCadiomJsonFromConfig(this IMvcBuilder builder, string jsonConfig)
    {
        var config = JsonConfiguration.FromJson(jsonConfig);
        return builder.AddGMCadiomJson(config.ProviderType, _ => { });
    }

    /// <summary>
    /// Extracts configuration from a provider instance.
    /// This is a helper method to get configuration details from an existing provider.
    /// </summary>
    private static JsonConfiguration GetConfigurationFromProvider(IJsonProvider provider)
    {
        // Since we can't directly access the internal configuration,
        // we'll create a default configuration with the provider type
        return new JsonConfiguration
        {
            ProviderType = provider.ProviderType
        };
    }
}
