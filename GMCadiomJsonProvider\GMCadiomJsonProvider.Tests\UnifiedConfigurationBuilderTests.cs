namespace GMCadiomJsonProvider.Tests;

public class UnifiedConfigurationBuilderTests
{
    private readonly TestPerson _testPerson;

    public UnifiedConfigurationBuilderTests()
    {
        _testPerson = new TestPerson
        {
            Id = 1,
            Name = "<PERSON>",
            Email = "<EMAIL>",
            DateOfBirth = new DateTime(1990, 1, 1),
            IsActive = true,
            Salary = null
        };
    }

    [Fact]
    public void Builder_UseConfiguration_ShouldApplyUnifiedConfig()
    {
        // Arrange
        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Newtonsoft,
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            NullValueHandling = JsonNullValueHandling.Ignore
        };

        // Act
        var provider = GMCadiomJson.CreateBuilder()
            .UseConfiguration(config)
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        json.Should().Contain("\n"); // Should be indented
        json.Should().Contain("\"name\""); // Should be camelCase
        json.Should().NotContain("\"salary\""); // Should ignore null values
    }

    [Fact]
    public void Builder_UseConfigurationAction_ShouldApplySettings()
    {
        // Act
        var provider = GMCadiomJson.CreateBuilder()
            .UseConfiguration(config =>
            {
                config.ProviderType = JsonProviderType.Microsoft;
                config.WriteIndented = true;
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                config.NullValueHandling = JsonNullValueHandling.Ignore;
            })
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        json.Should().Contain("\n"); // Should be indented
        json.Should().Contain("\"name\""); // Should be camelCase
        json.Should().NotContain("\"salary\""); // Should ignore null values
    }

    [Fact]
    public void Builder_UseJsonConfiguration_ShouldParseAndApply()
    {
        // Arrange
        var jsonConfig = """
        {
            "providerType": "Newtonsoft",
            "writeIndented": true,
            "propertyNamingPolicy": "CamelCase",
            "nullValueHandling": "Ignore"
        }
        """;

        // Act
        var provider = GMCadiomJson.CreateBuilder()
            .UseJsonConfiguration(jsonConfig)
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        json.Should().Contain("\n"); // Should be indented
        json.Should().Contain("\"name\""); // Should be camelCase
        json.Should().NotContain("\"salary\""); // Should ignore null values
    }

    [Fact]
    public void Builder_UnifiedConfigOverridesLegacyMethods_ShouldUseUnifiedConfig()
    {
        // Arrange
        var config = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Newtonsoft,
            WriteIndented = false // Unified config says no indentation
        };

        // Act
        var provider = GMCadiomJson.CreateBuilder()
            .WithIndentation(true) // Legacy method says indentation
            .UseConfiguration(config) // Unified config should override
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        json.Should().NotContain("\n"); // Should NOT be indented (unified config wins)
    }

    [Fact]
    public void Builder_LegacyMethodsWithoutUnifiedConfig_ShouldUseLegacyBehavior()
    {
        // Act
        var provider = GMCadiomJson.CreateBuilder()
            .UseNewtonsoft()
            .WithIndentation(true)
            .WithIgnoreNullValues(true)
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);
        json.Should().Contain("\n"); // Should be indented
        json.Should().NotContain("\"salary\""); // Should ignore null values
    }

    [Fact]
    public void Builder_ChainUnifiedConfigWithLegacyMethods_ShouldWorkCorrectly()
    {
        // Act
        var provider = GMCadiomJson.CreateBuilder()
            .UseConfiguration(config =>
            {
                config.ProviderType = JsonProviderType.Microsoft;
                config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            })
            .WithIndentation(true) // This should be ignored since unified config is used
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        json.Should().Contain("\"name\""); // Should be camelCase from unified config
        // Indentation behavior depends on unified config default (false)
    }

    [Fact]
    public void Builder_MultipleUnifiedConfigCalls_ShouldUseLastOne()
    {
        // Arrange
        var config1 = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Microsoft,
            WriteIndented = true
        };

        var config2 = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Newtonsoft,
            WriteIndented = false
        };

        // Act
        var provider = GMCadiomJson.CreateBuilder()
            .UseConfiguration(config1)
            .UseConfiguration(config2) // This should override config1
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        provider.ProviderType.Should().Be(JsonProviderType.Newtonsoft); // From config2
        json.Should().NotContain("\n"); // Should NOT be indented (from config2)
    }

    [Fact]
    public void Builder_ConfigurationModification_ShouldNotAffectOriginal()
    {
        // Arrange
        var originalConfig = new JsonConfiguration
        {
            ProviderType = JsonProviderType.Microsoft,
            WriteIndented = false
        };

        // Act
        var provider = GMCadiomJson.CreateBuilder()
            .UseConfiguration(originalConfig)
            .UseConfiguration(config => config.WriteIndented = true) // Modify the config
            .Build();

        var json = provider.Serialize(_testPerson);

        // Assert
        originalConfig.WriteIndented.Should().BeFalse(); // Original should be unchanged
        json.Should().Contain("\n"); // Provider should use modified config (indented)
    }

    [Fact]
    public void Builder_SnakeCaseNaming_ShouldWorkWithBothProviders()
    {
        // Test Microsoft provider
        var microsoftProvider = GMCadiomJson.CreateBuilder()
            .UseConfiguration(config =>
            {
                config.ProviderType = JsonProviderType.Microsoft;
                config.PropertyNamingPolicy = JsonNamingPolicy.SnakeCase;
            })
            .Build();

        var microsoftJson = microsoftProvider.Serialize(_testPerson);

        // Test Newtonsoft provider
        var newtonsoftProvider = GMCadiomJson.CreateBuilder()
            .UseConfiguration(config =>
            {
                config.ProviderType = JsonProviderType.Newtonsoft;
                config.PropertyNamingPolicy = JsonNamingPolicy.SnakeCase;
            })
            .Build();

        var newtonsoftJson = newtonsoftProvider.Serialize(_testPerson);

        // Assert
        microsoftProvider.ProviderType.Should().Be(JsonProviderType.Microsoft);
        newtonsoftProvider.ProviderType.Should().Be(JsonProviderType.Newtonsoft);

        microsoftJson.Should().Contain("\"date_of_birth\""); // snake_case
        newtonsoftJson.Should().Contain("\"date_of_birth\""); // snake_case
    }
}
