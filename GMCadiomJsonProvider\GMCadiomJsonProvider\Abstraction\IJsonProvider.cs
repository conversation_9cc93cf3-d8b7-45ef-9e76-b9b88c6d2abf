namespace GMCadiomJsonProvider.Abstraction;

/// <summary>
/// Defines the contract for JSON serialization and deserialization providers.
/// </summary>
public interface IJsonProvider
{
    /// <summary>
    /// Gets the type of the JSON provider.
    /// </summary>
    JsonProviderType ProviderType { get; }

    /// <summary>
    /// Gets the name of the JSON provider.
    /// </summary>
    string ProviderName { get; }

    /// <summary>
    /// Serializes an object to a JSON string.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <returns>A JSON string representation of the object.</returns>
    string Serialize<T>(T value);

    /// <summary>
    /// Serializes an object to a JSON string asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains a JSON string representation of the object.</returns>
    Task<string> SerializeAsync<T>(T value, CancellationToken cancellationToken = default);

    /// <summary>
    /// Serializes an object to a stream.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="stream">The stream to write the JSON data to.</param>
    /// <param name="value">The object to serialize.</param>
    void SerializeToStream<T>(Stream stream, T value);

    /// <summary>
    /// Serializes an object to a stream asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="stream">The stream to write the JSON data to.</param>
    /// <param name="value">The object to serialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    Task SerializeToStreamAsync<T>(Stream stream, T value, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <returns>The deserialized object.</returns>
    T? Deserialize<T>(string json);

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the deserialized object.</returns>
    Task<T?> DeserializeAsync<T>(string json, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <returns>The deserialized object.</returns>
    T? DeserializeFromStream<T>(Stream stream);

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type asynchronously.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <param name="cancellationToken">A cancellation token to cancel the operation.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the deserialized object.</returns>
    Task<T?> DeserializeFromStreamAsync<T>(Stream stream, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deserializes a JSON string to an object of the specified type.
    /// </summary>
    /// <param name="json">The JSON string to deserialize.</param>
    /// <param name="type">The type of the object to deserialize to.</param>
    /// <returns>The deserialized object.</returns>
    object? Deserialize(string json, Type type);

    /// <summary>
    /// Deserializes JSON data from a stream to an object of the specified type.
    /// </summary>
    /// <param name="stream">The stream containing the JSON data.</param>
    /// <param name="type">The type of the object to deserialize to.</param>
    /// <returns>The deserialized object.</returns>
    object? DeserializeFromStream(Stream stream, Type type);
}
