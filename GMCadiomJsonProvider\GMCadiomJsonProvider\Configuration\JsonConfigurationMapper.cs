namespace GMCadiomJsonProvider.Configuration;

/// <summary>
/// Maps unified JsonConfiguration to provider-specific options.
/// </summary>
public static class JsonConfigurationMapper
{
    /// <summary>
    /// Converts a unified JsonConfiguration to System.Text.Json JsonSerializerOptions.
    /// </summary>
    /// <param name="config">The unified configuration.</param>
    /// <returns>JsonSerializerOptions configured according to the unified config.</returns>
    public static System.Text.Json.JsonSerializerOptions ToSystemTextJsonOptions(JsonConfiguration config)
    {
        return config.ToSystemTextJsonOptions();
    }

    /// <summary>
    /// Converts a unified JsonConfiguration to Newtonsoft.Json JsonSerializerSettings.
    /// </summary>
    /// <param name="config">The unified configuration.</param>
    /// <returns>JsonSerializerSettings configured according to the unified config.</returns>
    public static Newtonsoft.Json.JsonSerializerSettings ToNewtonsoftJsonSettings(JsonConfiguration config)
    {
        return config.ToNewtonsoftJsonSettings();
    }


}
