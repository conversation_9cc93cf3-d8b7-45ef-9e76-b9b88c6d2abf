namespace GMCadiomJsonProvider.Builder;

/// <summary>
/// Builder for creating JSON providers with fluent configuration.
/// </summary>
public class JsonProviderBuilder : IJsonProviderBuilder
{
    private JsonConfiguration _config = new();

    /// <summary>
    /// Configures the builder to use Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseMicrosoft(Action<JsonConfiguration>? configure = null)
    {
        _config.ProviderType = JsonProviderType.Microsoft;
        configure?.Invoke(_config);
        return this;
    }

    /// <summary>
    /// Configures the builder to use Newtonsoft.Json provider.
    /// </summary>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseNewtonsoft(Action<JsonConfiguration>? configure = null)
    {
        _config.ProviderType = JsonProviderType.Newtonsoft;
        configure?.Invoke(_config);
        return this;
    }

    /// <summary>
    /// Configures the builder to use a specific provider type.
    /// </summary>
    /// <param name="providerType">The type of JSON provider to use.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseProvider(JsonProviderType providerType)
    {
        _config.ProviderType = providerType;
        return this;
    }

    /// <summary>
    /// Configures common JSON options.
    /// </summary>
    /// <param name="configure">Action to configure common options.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder ConfigureOptions(Action<JsonConfiguration> configure)
    {
        configure(_config);
        return this;
    }

    /// <summary>
    /// Sets whether to write indented JSON.
    /// </summary>
    /// <param name="writeIndented">True to write indented JSON; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder WithIndentation(bool writeIndented = true)
    {
        _config.WriteIndented = writeIndented;
        return this;
    }

    /// <summary>
    /// Sets whether property names should be case-insensitive during deserialization.
    /// </summary>
    /// <param name="caseInsensitive">True for case-insensitive property names; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder WithCaseInsensitiveProperties(bool caseInsensitive = true)
    {
        _config.PropertyNameCaseInsensitive = caseInsensitive;
        return this;
    }

    /// <summary>
    /// Sets whether to allow trailing commas in JSON.
    /// </summary>
    /// <param name="allowTrailingCommas">True to allow trailing commas; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder WithTrailingCommas(bool allowTrailingCommas = true)
    {
        _config.AllowTrailingCommas = allowTrailingCommas;
        return this;
    }

    /// <summary>
    /// Sets whether to ignore null values during serialization.
    /// </summary>
    /// <param name="ignoreNullValues">True to ignore null values; otherwise, false.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder WithIgnoreNullValues(bool ignoreNullValues = true)
    {
        _config.NullValueHandling = ignoreNullValues ? JsonNullValueHandling.Ignore : JsonNullValueHandling.Include;
        return this;
    }

    /// <summary>
    /// Configures the builder using a unified configuration.
    /// </summary>
    /// <param name="config">The unified JSON configuration.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseConfiguration(JsonConfiguration config)
    {
        _config = config.Clone();
        return this;
    }

    /// <summary>
    /// Configures the builder using a unified configuration action.
    /// </summary>
    /// <param name="configure">Action to configure the unified JSON configuration.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseConfiguration(Action<JsonConfiguration> configure)
    {
        configure(_config);
        return this;
    }

    /// <summary>
    /// Configures the builder from a JSON configuration string.
    /// </summary>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>The builder instance for method chaining.</returns>
    public IJsonProviderBuilder UseJsonConfiguration(string jsonConfig)
    {
        _config = JsonConfiguration.FromJson(jsonConfig);
        return this;
    }

    /// <summary>
    /// Builds the configured JSON provider.
    /// </summary>
    /// <returns>A configured JSON provider instance.</returns>
    public IJsonProvider Build()
    {
        return _config.ProviderType switch
        {
            JsonProviderType.Microsoft => new SystemTextJsonProvider(_config),
            JsonProviderType.Newtonsoft => new NewtonsoftJsonProvider(_config),
            _ => throw new ArgumentOutOfRangeException(nameof(_config.ProviderType), _config.ProviderType, "Unsupported provider type.")
        };
    }
}
