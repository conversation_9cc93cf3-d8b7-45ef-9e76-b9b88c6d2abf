namespace GMCadiomJsonProvider.Configuration;

/// <summary>
/// Defines the naming policy for JSON properties.
/// </summary>
public enum JsonNamingPolicy
{
    /// <summary>
    /// Use the original property names as-is.
    /// </summary>
    Default = 0,

    /// <summary>
    /// Convert property names to camelCase (e.g., "firstName").
    /// </summary>
    CamelCase = 1,

    /// <summary>
    /// Convert property names to snake_case (e.g., "first_name").
    /// </summary>
    SnakeCase = 2,

    /// <summary>
    /// Convert property names to kebab-case (e.g., "first-name").
    /// </summary>
    KebabCase = 3,

    /// <summary>
    /// Convert property names to <PERSON><PERSON><PERSON> (e.g., "FirstName").
    /// </summary>
    PascalCase = 4
}

/// <summary>
/// Defines how null values should be handled during serialization.
/// </summary>
public enum JsonNullValueHandling
{
    /// <summary>
    /// Include null values in the serialized JSON.
    /// </summary>
    Include = 0,

    /// <summary>
    /// Ignore null values during serialization.
    /// </summary>
    Ignore = 1,

    /// <summary>
    /// Only ignore null values when writing (serialization).
    /// </summary>
    IgnoreWhenWriting = 2
}

/// <summary>
/// Defines how dates should be formatted in JSON.
/// </summary>
public enum JsonDateFormatHandling
{
    /// <summary>
    /// Use ISO 8601 date format (default).
    /// </summary>
    IsoDateFormat = 0,

    /// <summary>
    /// Use Microsoft JSON date format.
    /// </summary>
    MicrosoftDateFormat = 1,

    /// <summary>
    /// Use Unix timestamp format.
    /// </summary>
    UnixTimeStamp = 2
}

/// <summary>
/// Defines how missing members should be handled during deserialization.
/// </summary>
public enum JsonMissingMemberHandling
{
    /// <summary>
    /// Ignore missing members (default).
    /// </summary>
    Ignore = 0,

    /// <summary>
    /// Throw an error when a member is missing.
    /// </summary>
    Error = 1
}

/// <summary>
/// Defines how reference loops should be handled.
/// </summary>
public enum JsonReferenceLoopHandling
{
    /// <summary>
    /// Throw an error when a reference loop is detected (default).
    /// </summary>
    Error = 0,

    /// <summary>
    /// Ignore reference loops.
    /// </summary>
    Ignore = 1,

    /// <summary>
    /// Serialize reference loops using object references.
    /// </summary>
    Serialize = 2
}
