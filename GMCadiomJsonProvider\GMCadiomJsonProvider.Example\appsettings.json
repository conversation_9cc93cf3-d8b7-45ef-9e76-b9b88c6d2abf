{"JsonProvider": {"providerType": "Microsoft", "writeIndented": true, "propertyNamingPolicy": "CamelCase", "nullValueHandling": "Ignore", "allowComments": true, "maxDepth": 128, "useRelaxedEscaping": false}, "JsonProviderProduction": {"providerType": "Microsoft", "writeIndented": false, "propertyNamingPolicy": "CamelCase", "nullValueHandling": "Ignore", "allowComments": false, "maxDepth": 64, "useRelaxedEscaping": true}, "JsonProviderNewtonsoft": {"providerType": "Newtonsoft", "writeIndented": true, "propertyNamingPolicy": "SnakeCase", "nullValueHandling": "Ignore", "dateFormatHandling": "IsoDateFormat", "missingMemberHandling": "Ignore", "referenceLoopHandling": "Ignore", "culture": "en-US", "maxDepth": 256}}