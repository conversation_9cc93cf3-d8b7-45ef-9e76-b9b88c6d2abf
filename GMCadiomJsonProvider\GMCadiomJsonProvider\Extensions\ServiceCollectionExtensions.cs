namespace GMCadiomJsonProvider.Extensions;

/// <summary>
/// Extension methods for IServiceCollection to register JSON providers.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds the JSON provider factory to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProviderFactory(this IServiceCollection services)
    {
        services.AddSingleton<IJsonProviderFactory, JsonProviderFactory>();
        return services;
    }

    /// <summary>
    /// Adds a JSON provider to the service collection using the default Microsoft provider.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProvider(this IServiceCollection services, Action<JsonConfiguration>? configure = null)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var config = new JsonConfiguration { ProviderType = JsonProviderType.Microsoft };
            configure?.Invoke(config);
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();
            return factory.CreateProvider(config);
        });

        return services;
    }

    /// <summary>
    /// Adds a Microsoft System.Text.Json provider to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddMicrosoftJsonProvider(this IServiceCollection services, Action<JsonConfiguration>? configure = null)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var config = new JsonConfiguration { ProviderType = JsonProviderType.Microsoft };
            configure?.Invoke(config);
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();
            return factory.CreateMicrosoftProvider(config);
        });

        return services;
    }

    /// <summary>
    /// Adds a Newtonsoft.Json provider to the service collection.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddNewtonsoftJsonProvider(this IServiceCollection services, Action<JsonConfiguration>? configure = null)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var config = new JsonConfiguration { ProviderType = JsonProviderType.Newtonsoft };
            configure?.Invoke(config);
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();
            return factory.CreateNewtonsoftProvider(config);
        });

        return services;
    }

    /// <summary>
    /// Adds a JSON provider to the service collection using a builder for fluent configuration.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="configure">Action to configure the JSON provider using the builder.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProvider(this IServiceCollection services, Action<IJsonProviderBuilder> configure)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var builder = new JsonProviderBuilder();
            configure(builder);
            return builder.Build();
        });

        return services;
    }



    /// <summary>
    /// Adds a JSON provider to the service collection using unified configuration.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="config">The unified JSON configuration.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProvider(this IServiceCollection services, JsonConfiguration config)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();
            return factory.CreateProvider(config);
        });

        return services;
    }



    /// <summary>
    /// Adds a JSON provider to the service collection from a JSON configuration string.
    /// </summary>
    /// <param name="services">The service collection.</param>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>The service collection for method chaining.</returns>
    public static IServiceCollection AddJsonProviderFromJson(this IServiceCollection services, string jsonConfig)
    {
        services.AddJsonProviderFactory();

        services.AddSingleton<IJsonProvider>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<IJsonProviderFactory>();
            return factory.CreateProviderFromJson(jsonConfig);
        });

        return services;
    }
}
