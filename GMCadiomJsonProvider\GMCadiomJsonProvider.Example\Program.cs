﻿Console.WriteLine("=== GMCadiomJsonProvider Example ===\n");

// Create sample data
var person = new Person
{
    Id = 1,
    Name = "<PERSON>",
    Email = "<EMAIL>",
    DateOfBirth = new DateTime(1990, 1, 1),
    IsActive = true,
    Salary = 75000.50m,
    Tags = new List<string> { "developer", "senior", "full-stack" },
    Address = new Address
    {
        Street = "123 Main St",
        City = "New York",
        Country = "USA",
        PostalCode = "10001"
    }
};

Console.WriteLine("1. Using Static Helper Methods (Default Microsoft Provider):");
Console.WriteLine("==========================================================");

// Serialize using default provider
var json = GMCadiomJson.Serialize(person);
Console.WriteLine($"Serialized JSON:\n{json}\n");

// Deserialize
var deserializedPerson = GMCadiomJson.Deserialize<Person>(json);
Console.WriteLine($"Deserialized: {deserializedPerson?.Name} ({deserializedPerson?.Email})\n");

Console.WriteLine("2. Using Builder Pattern with Newtonsoft Provider:");
Console.WriteLine("==================================================");

// Create provider using builder pattern
var newtonsoftProvider = GMCadiomJson.CreateBuilder()
    .UseNewtonsoft()
    .WithIndentation(true)
    .WithIgnoreNullValues(true)
    .Build();

var newtonsoftJson = newtonsoftProvider.Serialize(person);
Console.WriteLine($"Newtonsoft JSON (indented):\n{newtonsoftJson}\n");

Console.WriteLine("3. Using Factory Pattern:");
Console.WriteLine("=========================");

// Create providers using factory
var microsoftProvider = GMCadiomJson.Factory.CreateMicrosoftProvider();
var anotherNewtonsoftProvider = GMCadiomJson.Factory.CreateNewtonsoftProvider();

Console.WriteLine($"Microsoft Provider: {microsoftProvider.ProviderName} ({microsoftProvider.ProviderType})");
Console.WriteLine($"Newtonsoft Provider: {anotherNewtonsoftProvider.ProviderName} ({anotherNewtonsoftProvider.ProviderType})\n");

Console.WriteLine("4. Comparing Providers:");
Console.WriteLine("======================");

var microsoftJson = microsoftProvider.Serialize(person);
var newtonsoftJsonCompact = anotherNewtonsoftProvider.Serialize(person);

Console.WriteLine($"Microsoft JSON length: {microsoftJson.Length} characters");
Console.WriteLine($"Newtonsoft JSON length: {newtonsoftJsonCompact.Length} characters\n");

Console.WriteLine("5. Using Configuration:");
Console.WriteLine("======================");

// Create configured providers
var configuredMicrosoft = GMCadiomJson.CreateMicrosoft(config =>
{
    config.WriteIndented = true;
    config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    config.NullValueHandling = JsonNullValueHandling.Ignore;
});

var configuredNewtonsoft = GMCadiomJson.CreateNewtonsoft(config =>
{
    config.WriteIndented = true;
    config.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    config.NullValueHandling = JsonNullValueHandling.Ignore;
});

var microsoftCamelCase = configuredMicrosoft.Serialize(person);
var newtonsoftCamelCase = configuredNewtonsoft.Serialize(person);

Console.WriteLine("Microsoft with camelCase:");
Console.WriteLine(microsoftCamelCase);
Console.WriteLine("\nNewtonsoft with camelCase:");
Console.WriteLine(newtonsoftCamelCase);

Console.WriteLine("\n6. Async Operations:");
Console.WriteLine("===================");

// Async operations
var asyncJson = await GMCadiomJson.SerializeAsync(person);
var asyncPerson = await GMCadiomJson.DeserializeAsync<Person>(asyncJson);
Console.WriteLine($"Async result: {asyncPerson?.Name} - {asyncPerson?.Address?.City}");

Console.WriteLine("\n=== Example Complete ===");

Console.WriteLine("\n" + new string('=', 60));
Console.WriteLine("Press any key to see the Unified Configuration example...");
Console.ReadKey();
Console.Clear();

// Run the unified configuration example
UnifiedConfigurationExample.RunExample();

Console.WriteLine("\n" + new string('=', 60));
Console.WriteLine("Press any key to see the ASP.NET Core Integration examples...");
Console.ReadKey();
Console.Clear();

// Run the ASP.NET Core integration examples
AspNetCoreIntegrationExample.ConfigureWebApplication();
AspNetCoreIntegrationExample.AdvancedConfigurationExamples();

// Example data model
public class Person
{
    public int Id { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public DateTime DateOfBirth { get; set; }
    public bool IsActive { get; set; }
    public decimal? Salary { get; set; }
    public List<string>? Tags { get; set; }
    public Address? Address { get; set; }
}

public class Address
{
    public string? Street { get; set; }
    public string? City { get; set; }
    public string? Country { get; set; }
    public string? PostalCode { get; set; }
}
