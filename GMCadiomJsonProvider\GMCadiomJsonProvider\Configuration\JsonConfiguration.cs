namespace GMCadiomJsonProvider.Configuration;

/// <summary>
/// Unified configuration for JSON providers that maps to provider-specific settings.
/// </summary>
public class JsonConfiguration
{
    /// <summary>
    /// Gets or sets the JSON provider type to use.
    /// </summary>
    public JsonProviderType ProviderType { get; set; } = JsonProviderType.Microsoft;

    /// <summary>
    /// Gets or sets whether to write indented (pretty-printed) JSON.
    /// </summary>
    public bool WriteIndented { get; set; } = false;

    /// <summary>
    /// Gets or sets whether property names should be case-insensitive during deserialization.
    /// </summary>
    public bool PropertyNameCaseInsensitive { get; set; } = false;

    /// <summary>
    /// Gets or sets whether to allow trailing commas in JSON.
    /// </summary>
    public bool AllowTrailingCommas { get; set; } = false;

    /// <summary>
    /// Gets or sets how null values should be handled during serialization.
    /// </summary>
    public JsonNullValueHandling NullValueHandling { get; set; } = JsonNullValueHandling.Include;

    /// <summary>
    /// Gets or sets the naming policy for JSON properties.
    /// </summary>
    public JsonNamingPolicy PropertyNamingPolicy { get; set; } = JsonNamingPolicy.Default;

    /// <summary>
    /// Gets or sets the naming policy for dictionary keys.
    /// </summary>
    public JsonNamingPolicy DictionaryKeyNamingPolicy { get; set; } = JsonNamingPolicy.Default;

    /// <summary>
    /// Gets or sets how dates should be formatted in JSON.
    /// </summary>
    public JsonDateFormatHandling DateFormatHandling { get; set; } = JsonDateFormatHandling.IsoDateFormat;

    /// <summary>
    /// Gets or sets how missing members should be handled during deserialization.
    /// </summary>
    public JsonMissingMemberHandling MissingMemberHandling { get; set; } = JsonMissingMemberHandling.Ignore;

    /// <summary>
    /// Gets or sets how reference loops should be handled.
    /// </summary>
    public JsonReferenceLoopHandling ReferenceLoopHandling { get; set; } = JsonReferenceLoopHandling.Error;

    /// <summary>
    /// Gets or sets whether to include fields during serialization and deserialization.
    /// </summary>
    public bool IncludeFields { get; set; } = false;

    /// <summary>
    /// Gets or sets the maximum depth allowed when reading JSON.
    /// </summary>
    public int MaxDepth { get; set; } = 64;

    /// <summary>
    /// Gets or sets whether to allow reading comments in JSON.
    /// </summary>
    public bool AllowComments { get; set; } = false;

    /// <summary>
    /// Gets or sets whether numbers can be read from strings.
    /// </summary>
    public bool AllowNumbersFromStrings { get; set; } = false;

    /// <summary>
    /// Gets or sets the culture to use for parsing and formatting.
    /// </summary>
    public string? Culture { get; set; }

    /// <summary>
    /// Gets or sets custom date format string.
    /// </summary>
    public string? CustomDateFormat { get; set; }

    /// <summary>
    /// Gets or sets whether to use relaxed JSON escaping.
    /// </summary>
    public bool UseRelaxedEscaping { get; set; } = false;

    /// <summary>
    /// Creates a copy of the current configuration.
    /// </summary>
    /// <returns>A new instance with the same configuration.</returns>
    public JsonConfiguration Clone()
    {
        return new JsonConfiguration
        {
            ProviderType = ProviderType,
            WriteIndented = WriteIndented,
            PropertyNameCaseInsensitive = PropertyNameCaseInsensitive,
            AllowTrailingCommas = AllowTrailingCommas,
            NullValueHandling = NullValueHandling,
            PropertyNamingPolicy = PropertyNamingPolicy,
            DictionaryKeyNamingPolicy = DictionaryKeyNamingPolicy,
            DateFormatHandling = DateFormatHandling,
            MissingMemberHandling = MissingMemberHandling,
            ReferenceLoopHandling = ReferenceLoopHandling,
            IncludeFields = IncludeFields,
            MaxDepth = MaxDepth,
            AllowComments = AllowComments,
            AllowNumbersFromStrings = AllowNumbersFromStrings,
            Culture = Culture,
            CustomDateFormat = CustomDateFormat,
            UseRelaxedEscaping = UseRelaxedEscaping
        };
    }

    /// <summary>
    /// Creates a JsonConfiguration from a JSON string.
    /// </summary>
    /// <param name="json">The JSON configuration string.</param>
    /// <returns>A JsonConfiguration instance.</returns>
    public static JsonConfiguration FromJson(string json)
    {
        var options = new System.Text.Json.JsonSerializerOptions
        {
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
            Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
        };
        return System.Text.Json.JsonSerializer.Deserialize<JsonConfiguration>(json, options) ?? new JsonConfiguration();
    }

    /// <summary>
    /// Converts the configuration to a JSON string.
    /// </summary>
    /// <param name="writeIndented">Whether to write indented JSON.</param>
    /// <returns>A JSON representation of the configuration.</returns>
    public string ToJson(bool writeIndented = true)
    {
        var options = new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = writeIndented,
            PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
            Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
        };
        return System.Text.Json.JsonSerializer.Serialize(this, options);
    }

    /// <summary>
    /// Converts the unified configuration to System.Text.Json JsonSerializerOptions.
    /// </summary>
    /// <returns>A configured JsonSerializerOptions instance.</returns>
    public System.Text.Json.JsonSerializerOptions ToSystemTextJsonOptions()
    {
        var options = new System.Text.Json.JsonSerializerOptions
        {
            WriteIndented = WriteIndented,
            PropertyNameCaseInsensitive = PropertyNameCaseInsensitive,
            AllowTrailingCommas = AllowTrailingCommas,
            IncludeFields = IncludeFields,
            MaxDepth = MaxDepth
        };

        // Map null value handling
        switch (NullValueHandling)
        {
            case JsonNullValueHandling.Include:
                options.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never;
                break;
            case JsonNullValueHandling.Ignore:
            case JsonNullValueHandling.IgnoreWhenWriting:
                options.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
                break;
        }

        // Map property naming policy
        options.PropertyNamingPolicy = MapNamingPolicyToSystemTextJson(PropertyNamingPolicy);
        options.DictionaryKeyPolicy = MapNamingPolicyToSystemTextJson(DictionaryKeyNamingPolicy);

        // Map comment handling
        if (AllowComments)
        {
            options.ReadCommentHandling = System.Text.Json.JsonCommentHandling.Skip;
        }

        // Map number handling
        if (AllowNumbersFromStrings)
        {
            options.NumberHandling = System.Text.Json.Serialization.JsonNumberHandling.AllowReadingFromString;
        }

        // Map encoder for relaxed escaping
        if (UseRelaxedEscaping)
        {
            options.Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
        }

        return options;
    }

    /// <summary>
    /// Converts the unified configuration to Newtonsoft.Json JsonSerializerSettings.
    /// </summary>
    /// <returns>A configured JsonSerializerSettings instance.</returns>
    public Newtonsoft.Json.JsonSerializerSettings ToNewtonsoftJsonSettings()
    {
        var settings = new Newtonsoft.Json.JsonSerializerSettings
        {
            Formatting = WriteIndented ? Newtonsoft.Json.Formatting.Indented : Newtonsoft.Json.Formatting.None
        };

        // Map null value handling
        switch (NullValueHandling)
        {
            case JsonNullValueHandling.Include:
                settings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Include;
                break;
            case JsonNullValueHandling.Ignore:
            case JsonNullValueHandling.IgnoreWhenWriting:
                settings.NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore;
                break;
        }

        // Map date format handling
        switch (DateFormatHandling)
        {
            case JsonDateFormatHandling.IsoDateFormat:
                settings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.IsoDateFormat;
                break;
            case JsonDateFormatHandling.MicrosoftDateFormat:
                settings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.MicrosoftDateFormat;
                break;
            case JsonDateFormatHandling.UnixTimeStamp:
                // Newtonsoft doesn't have direct Unix timestamp support, use ISO as fallback
                settings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.IsoDateFormat;
                break;
        }

        // Map missing member handling
        switch (MissingMemberHandling)
        {
            case JsonMissingMemberHandling.Ignore:
                settings.MissingMemberHandling = Newtonsoft.Json.MissingMemberHandling.Ignore;
                break;
            case JsonMissingMemberHandling.Error:
                settings.MissingMemberHandling = Newtonsoft.Json.MissingMemberHandling.Error;
                break;
        }

        // Map reference loop handling
        switch (ReferenceLoopHandling)
        {
            case JsonReferenceLoopHandling.Error:
                settings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Error;
                break;
            case JsonReferenceLoopHandling.Ignore:
                settings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
                break;
            case JsonReferenceLoopHandling.Serialize:
                settings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Serialize;
                break;
        }

        // Map property naming policy
        settings.ContractResolver = MapNamingPolicyToNewtonsoft(PropertyNamingPolicy);

        // Map culture
        if (!string.IsNullOrEmpty(Culture))
        {
            try
            {
                settings.Culture = System.Globalization.CultureInfo.GetCultureInfo(Culture);
            }
            catch
            {
                // Ignore invalid culture names
            }
        }

        // Map max depth
        if (MaxDepth > 0)
        {
            settings.MaxDepth = MaxDepth;
        }

        return settings;
    }

    /// <summary>
    /// Maps unified naming policy to System.Text.Json naming policy.
    /// </summary>
    private static System.Text.Json.JsonNamingPolicy? MapNamingPolicyToSystemTextJson(JsonNamingPolicy policy)
    {
        return policy switch
        {
            JsonNamingPolicy.CamelCase => System.Text.Json.JsonNamingPolicy.CamelCase,
            JsonNamingPolicy.SnakeCase => System.Text.Json.JsonNamingPolicy.SnakeCaseLower,
            JsonNamingPolicy.KebabCase => System.Text.Json.JsonNamingPolicy.KebabCaseLower,
            _ => null
        };
    }

    /// <summary>
    /// Maps unified naming policy to Newtonsoft.Json contract resolver.
    /// </summary>
    private static Newtonsoft.Json.Serialization.IContractResolver? MapNamingPolicyToNewtonsoft(JsonNamingPolicy policy)
    {
        return policy switch
        {
            JsonNamingPolicy.CamelCase => new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver(),
            JsonNamingPolicy.SnakeCase => new Newtonsoft.Json.Serialization.DefaultContractResolver
            {
                NamingStrategy = new Newtonsoft.Json.Serialization.SnakeCaseNamingStrategy()
            },
            JsonNamingPolicy.KebabCase => new Newtonsoft.Json.Serialization.DefaultContractResolver
            {
                NamingStrategy = new Newtonsoft.Json.Serialization.KebabCaseNamingStrategy()
            },
            _ => null
        };
    }
}
